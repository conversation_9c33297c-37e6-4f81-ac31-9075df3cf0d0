const axios = require('axios');

async function testAPI() {
  try {
    // Test without auth first
    console.log('Testing API without auth...');
    const response1 = await axios.get('http://localhost:5000/api/v1/tenants');
    console.log('Response without auth:', response1.status);
  } catch (error) {
    console.log('Expected error without auth:', error.response?.status);
  }

  try {
    // Test with auth token
    console.log('Testing API with auth...');
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4NGJmYWMwN2E4ZDljMmMwOThhYTVkYSIsImlhdCI6MTczNDEzNzc4NCwiZXhwIjoxNzM2NzI5Nzg0fQ.38cyYdcc-w';
    
    const response2 = await axios.get('http://localhost:5000/api/v1/tenants', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('Response with auth:', response2.status);
    console.log('Data:', response2.data);
  } catch (error) {
    console.log('Error with auth:', error.response?.status, error.response?.data);
  }
}

testAPI();
