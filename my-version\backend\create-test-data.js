const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const Admin = require('./models/Admin');
const Room = require('./models/Room');
const Tenant = require('./models/Tenant');

async function createTestData() {
  try {
    await mongoose.connect('mongodb://localhost:27017/pg-management');
    console.log('Connected to MongoDB');

    // Clear existing data
    await Admin.deleteMany({});
    await Room.deleteMany({});
    await Tenant.deleteMany({});
    console.log('Cleared existing data');

    // Create test admin
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);
    
    const admin = await Admin.create({
      name: 'Test Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      pgName: 'Test PG',
      phone: '1234567890',
      address: 'Test Address'
    });
    
    console.log('Created admin:', admin.name, admin._id);

    // Create test rooms
    const room1 = await Room.create({
      floorNumber: 1,
      roomNumber: '101',
      capacity: 4,
      rentAmount: 5000,
      amenities: ['Wifi', 'AC', 'TV'],
      description: 'Spacious room with all amenities',
      adminId: admin._id
    });

    const room2 = await Room.create({
      floorNumber: 1,
      roomNumber: '102',
      capacity: 2,
      rentAmount: 6000,
      amenities: ['Wifi', 'AC'],
      description: 'Cozy room for two',
      adminId: admin._id
    });

    console.log('Created rooms:', room1.roomNumber, room2.roomNumber);

    // Create test tenants
    const tenant1 = await Tenant.create({
      name: 'John Doe',
      phone: '9876543210',
      email: '<EMAIL>',
      idProofType: 'Aadhar',
      idProofNumber: 'AADHAR123456',
      occupation: 'Software Engineer',
      roomId: room1._id,
      bedNumber: 1,
      adminId: admin._id
    });

    const tenant2 = await Tenant.create({
      name: 'Jane Smith',
      phone: '**********',
      email: '<EMAIL>',
      idProofType: 'PAN',
      idProofNumber: 'PAN123456',
      occupation: 'Designer',
      roomId: room1._id,
      bedNumber: 2,
      adminId: admin._id
    });

    const tenant3 = await Tenant.create({
      name: 'Bob Wilson',
      phone: '**********',
      email: '<EMAIL>',
      idProofType: 'Driving License',
      idProofNumber: 'DL123456',
      occupation: 'Student',
      roomId: room2._id,
      bedNumber: 1,
      adminId: admin._id
    });

    console.log('Created tenants:', tenant1.name, tenant2.name, tenant3.name);

    // Update room occupancy
    await Room.findByIdAndUpdate(room1._id, {
      occupiedBeds: 2,
      isOccupied: false
    });

    await Room.findByIdAndUpdate(room2._id, {
      occupiedBeds: 1,
      isOccupied: false
    });

    console.log('Updated room occupancy');

    console.log('\n=== TEST DATA CREATED SUCCESSFULLY ===');
    console.log('Admin Email: <EMAIL>');
    console.log('Admin Password: password123');
    console.log('Rooms: 2 rooms created');
    console.log('Tenants: 3 tenants created');
    console.log('Room 101: 2/4 beds occupied (John Doe, Jane Smith)');
    console.log('Room 102: 1/2 beds occupied (Bob Wilson)');

    process.exit(0);
  } catch (error) {
    console.error('Error creating test data:', error);
    process.exit(1);
  }
}

createTestData();
