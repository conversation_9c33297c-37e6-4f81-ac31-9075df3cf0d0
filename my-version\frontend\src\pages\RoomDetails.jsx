import React, { useEffect, useState } from "react";
import { useParams, useNavigate, Link, useLocation } from "react-router-dom";
import {
  FaArrowLeft,
  FaUserPlus,
  FaUser,
  FaBed,
  FaTrash,
  FaEdit,
  FaInfoCircle,
  FaBuilding,
  FaRupeeSign,
  FaUsers,
  FaLayerGroup,
  FaListUl,
  FaCalendarAlt,
  FaEnvelope,
  FaPhone,
  FaCheckCircle,
  FaTimesCircle,
  FaWifi,
  FaSnowflake,
  FaTv,
  FaShower,
  FaChair,
  FaToilet,
  FaUtensils,
  FaFan,
  FaCouch,
  FaArchway,
  FaWarehouse,
  FaDoorOpen,
} from "react-icons/fa";
import { toast } from "react-toastify";
import { useRooms } from "../context/RoomContext";
import { useTenants } from "../context/TenantContext";
import { useAuth } from "../context/AuthContext";

const RoomDetails = ({ roomProp, isModal }) => {
  const { id: paramId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { rooms, loading: roomsLoading, deleteRoom, fetchRooms, fixRoomOccupancy } = useRooms();
  const { tenants, loading: tenantsLoading, deleteTenant, fetchTenants } = useTenants();
  const { admin } = useAuth();
  const [room, setRoom] = useState(null);
  const [roomTenants, setRoomTenants] = useState([]);
  const id = roomProp?._id || paramId;

  // Use a ref to track if we've already set the initial data
  const [isInitialized, setIsInitialized] = useState(false);

  // Get highlighted bed from query param (for onboarding success)
  const queryParams = new URLSearchParams(location.search);
  const highlightBed = parseInt(queryParams.get("highlightBed"), 10);

  // Main effect to set room and tenant data
  useEffect(() => {
    // Skip if we're still loading or don't have the necessary data
    if (roomsLoading || tenantsLoading) return;

    if (roomProp) {
      setRoom(roomProp);

      // Handle both populated and non-populated roomId in tenants
      const filteredTenants = tenants.filter((t) => {
        if (typeof t.roomId === "object" && t.roomId !== null) {
          return t.roomId._id === roomProp._id;
        }
        return t.roomId === roomProp._id;
      });

      setRoomTenants(filteredTenants);
      console.log("RoomDetails: Updated tenants for room", roomProp._id, ":", filteredTenants);
    } else {
      const foundRoom = rooms.find((r) => r._id === id);
      if (foundRoom) {
        setRoom(foundRoom);
        console.log("RoomDetails: Updated room data:", foundRoom);
      }

      // Handle both populated and non-populated roomId in tenants
      const filteredTenants = tenants.filter((t) => {
        if (typeof t.roomId === "object" && t.roomId !== null) {
          return t.roomId._id === id;
        }
        return t.roomId === id;
      });

      setRoomTenants(filteredTenants);
      console.log("RoomDetails: Updated tenants for room", id, ":", filteredTenants);
    }

    setIsInitialized(true);
  }, [id, rooms, tenants, roomProp, roomsLoading, tenantsLoading]);

  // Add effect to force refresh when returning from tenant addition
  useEffect(() => {
    const handleFocus = async () => {
      console.log("RoomDetails: Window focused, refreshing data");
      // Refresh data from server when window gains focus (user returns from tenant form)
      try {
        await fetchRooms();
        await fetchTenants();
      } catch (error) {
        console.error("Error refreshing data:", error);
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [fetchRooms, fetchTenants]);

  // Add effect to refresh data when URL changes (e.g., returning from tenant form)
  useEffect(() => {
    const refreshData = async () => {
      console.log("RoomDetails: URL changed, refreshing data");
      try {
        await fetchRooms();
        await fetchTenants();
      } catch (error) {
        console.error("Error refreshing data:", error);
      }
    };

    // Check if we're returning from tenant addition (has highlightBed param)
    if (highlightBed) {
      refreshData();
    }
  }, [location.search, fetchRooms, fetchTenants, highlightBed]);

  // Function to handle adding a tenant to the room
  const handleAddTenant = (bedNumber = null) => {
    // If a specific bed number is provided, include it in the URL
    const bedParam = bedNumber ? `&bedNumber=${bedNumber}` : '';
    navigate(`/tenants/add?roomId=${id}&returnToRoom=true${bedParam}`);
  };

  const handleDeleteTenant = async (tenantId) => {
    if (window.confirm("Are you sure you want to delete this tenant?")) {
      await deleteTenant(tenantId);
      // Force refresh after deletion
      setTimeout(async () => {
        await fetchRooms();
        await fetchTenants();
      }, 500);
    }
  };

  const handleManualRefresh = async () => {
    console.log("Manual refresh triggered");
    try {
      await fetchRooms();
      await fetchTenants();
      console.log("Manual refresh completed");
    } catch (error) {
      console.error("Error during manual refresh:", error);
    }
  };

  // Show a loading state only on initial load, not during updates
  if (!isInitialized) {
    return (
      <div className="premium-loading">
        <div className="premium-spinner"></div>
        <p className="premium-loading-text">Loading room details...</p>
      </div>
    );
  }

  if (!room) {
    return (
      <div className="premium-error-container">
        <h2 className="premium-error-heading">Room Not Found</h2>
        <button
          onClick={() => navigate("/rooms")}
          className="premium-details-back"
        >
          <FaArrowLeft className="premium-details-back-icon" /> Back to Rooms
        </button>
      </div>
    );
  }

  // Amenity icons
  const amenityIcons = {
    Wifi: <FaWifi />,
    AC: <FaSnowflake />,
    TV: <FaTv />,
    Shower: <FaShower />,
    Chair: <FaChair />,
    Toilet: <FaToilet />,
    Kitchen: <FaUtensils />,
    Fan: <FaFan />,
    Sofa: <FaCouch />,
    Hall: <FaArchway />,
    Store: <FaWarehouse />,
    Door: <FaDoorOpen />,
    List: <FaListUl />,
  };

  // Function to handle room deletion
  const handleDeleteRoom = async () => {
    if (window.confirm("Are you sure you want to delete this room?")) {
      const success = await deleteRoom(id);
      if (success) {
        navigate("/rooms");
      }
    }
  };

  // Function to render bed layout
  const renderBedLayout = () => {
    if (!room) return null;

    // Map tenants to beds by bedNumber
    const bedTenants = Array(room.capacity).fill(null);
    roomTenants.forEach((tenant) => {
      if (tenant.bedNumber && tenant.bedNumber <= room.capacity) {
        bedTenants[tenant.bedNumber - 1] = tenant;
      }
    });

    console.log("RoomDetails: Rendering bed layout for room", room._id);
    console.log("RoomDetails: Room tenants:", roomTenants);
    console.log("RoomDetails: Bed tenants mapping:", bedTenants);

    const beds = [];
    for (let i = 0; i < room.capacity; i++) {
      const bedNumber = i + 1;
      const tenant = bedTenants[i];
      const isOccupied = !!tenant;
      const isHighlighted = highlightBed === bedNumber;

      console.log(`RoomDetails: Bed ${bedNumber} - isOccupied: ${isOccupied}, tenant:`, tenant);
      console.log(`RoomDetails: Bed ${bedNumber} - Colors - border: ${isOccupied || isHighlighted ? "#ef4444" : "#10b981"}, bg: ${isOccupied || isHighlighted ? "#ffebeb" : "#e6fff5"}`);
      
      beds.push(
        <div
          key={i}
          className={`bed-item ${isOccupied || isHighlighted ? "occupied" : "vacant"}`}
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "space-between",
            width: "180px",
            height: "280px",
            margin: "10px",
            borderRadius: "12px",
            border: "1px solid",
            borderColor: isOccupied || isHighlighted ? "#ef4444" : "#10b981",
            backgroundColor: isOccupied || isHighlighted ? "#ffebeb" : "#e6fff5",
            boxShadow: isOccupied || isHighlighted
              ? "0 2px 8px rgba(239, 68, 68, 0.15)"
              : "0 1px 3px rgba(0,0,0,0.05)",
            padding: "20px 15px",
            position: "relative",
            transition: "all 0.2s ease",
            cursor: "pointer",
          }}
          onClick={isOccupied ? () => navigate(`/tenants/edit/${tenant?._id}`) : () => handleAddTenant(bedNumber)}
        >
          <div style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "8px",
          }}>
            <FaBed style={{ fontSize: "24px", color: isOccupied || isHighlighted ? "#ef4444" : "#10b981" }} />
            <span style={{ fontWeight: "600", color: isOccupied || isHighlighted ? "#ef4444" : "#10b981" }}>
              Bed {bedNumber}
            </span>
          </div>
          
          {isOccupied ? (
            <div style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "4px",
            }}>
              <FaUser style={{ fontSize: "20px", color: "#ef4444" }} />
              <span style={{ fontSize: "14px", color: "#ef4444", textAlign: "center" }}>
                {tenant.name}
              </span>
            </div>
          ) : (
            <div style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              gap: "4px",
            }}>
              <FaUserPlus style={{ fontSize: "20px", color: "#10b981" }} />
              <span style={{ fontSize: "14px", color: "#10b981" }}>
                Add Tenant
              </span>
            </div>
          )}
        </div>
      );
    }

    return (
      <div style={{
        display: "flex",
        flexWrap: "wrap",
        justifyContent: "center",
        gap: "20px",
        padding: "20px",
      }}>
        {beds}
      </div>
    );
  };

  return (
    <div
      className="modern-room-details-glass"
      style={{
        maxWidth: 800,
        margin: "2.5rem auto",
        borderRadius: 22,
        boxShadow: "0 8px 32px rgba(80,80,120,0.13)",
        overflow: "hidden",
        background: "rgba(255,255,255,0.85)",
        backdropFilter: "blur(12px)",
        position: "relative",
      }}
    >
      <div
        className="modern-room-details-header"
        style={{
          background: "linear-gradient(135deg, #6366f1 0%, #4f46e5 100%)",
          color: "white",
          padding: "2rem 2.5rem 1.5rem 2.5rem",
          borderTopLeftRadius: 22,
          borderTopRightRadius: 22,
          display: "flex",
          flexDirection: "column",
          gap: 8,
        }}
      >
        <div style={{ fontSize: "0.9rem", color: "rgba(255, 255, 255, 0.8)", marginBottom: "10px" }}>
          Logged in as: <span style={{ fontWeight: "bold" }}>{admin?.name || 'User'}</span> ({admin?.pgName || 'PG Name'})
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            gap: 10,
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: 10 }}>
            <FaBed style={{ fontSize: 24 }} />
            <span style={{ fontWeight: 700, fontSize: 20 }}>
              Room {room?.roomNumber}
            </span>
          </div>
          <span
            style={{
              display: "flex",
              alignItems: "center",
              gap: 6,
              background:
                room?.occupiedBeds < room?.capacity ? "#10b981" : "#ef4444",
              color: "white",
              borderRadius: 8,
              padding: "2px 12px",
              fontSize: 14,
              fontWeight: 600,
            }}
          >
            {room?.occupiedBeds < room?.capacity ? (
              <FaCheckCircle />
            ) : (
              <FaTimesCircle />
            )}{" "}
            {room?.occupiedBeds < room?.capacity ? "Vacant" : "Full"}
          </span>
        </div>
        <div
          style={{ display: "flex", gap: 10, marginTop: 8, flexWrap: "wrap" }}
        >
          <span className="modern-pill-badge">
            <FaBuilding /> Floor {room?.floorNumber}
          </span>
          <span className="modern-pill-badge">
            <FaUsers /> {room?.occupiedBeds}/{room?.capacity} Beds
          </span>
          <span className="modern-pill-badge">
            <FaRupeeSign /> {room?.rentAmount}
          </span>
        </div>
      </div>
      <div style={{ padding: "2rem 2.5rem" }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: 8,
            marginBottom: 10,
          }}
        >
          <FaListUl style={{ color: "#6366f1", fontSize: 18 }} />
          <span style={{ fontWeight: 600, fontSize: 16, color: "#22223b" }}>
            Amenities
          </span>
        </div>
        <div
          style={{
            display: "flex",
            gap: 10,
            flexWrap: "wrap",
            marginBottom: 18,
          }}
        >
          {room?.amenities && room.amenities.length > 0 ? (
            room.amenities.map((a) => (
              <span
                key={a}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 5,
                  background: "#f3f4f6",
                  borderRadius: 8,
                  padding: "5px 12px",
                  fontSize: 14,
                  color: "#6366f1",
                  fontWeight: 500,
                }}
              >
                {amenityIcons[a] || <FaListUl />} {a}
              </span>
            ))
          ) : (
            <span style={{ color: "#aaa" }}>No amenities listed</span>
          )}
        </div>
        {/* Bed Layout Section */}
        <div style={{ marginTop: 18, marginBottom: 10 }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              marginBottom: 8,
            }}
          >
            <span
              style={{
                fontWeight: 700,
                fontSize: 15,
                color: "#6366f1",
                letterSpacing: 0.01,
              }}
            >
              Bed Layout
            </span>
            <button
              onClick={handleManualRefresh}
              style={{
                padding: "8px 16px",
                backgroundColor: "#6366f1",
                color: "white",
                border: "none",
                borderRadius: "6px",
                fontSize: "12px",
                cursor: "pointer",
              }}
            >
              🔄 Refresh Data
            </button>
          </div>
          <div
            style={{
              background: "white",
              borderRadius: "12px",
              padding: "20px 15px",
              border: "1px solid #e5e7eb",
              boxShadow: "0 2px 10px rgba(0,0,0,0.05)",
              transition: "all 0.3s ease",
            }}
          >
            {renderBedLayout()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomDetails;
