const mongoose = require("mongoose");

const connectDB = async () => {
  try {
    // Use hardcoded MongoDB URI if environment variable is not available
    const mongoURI = process.env.NODE_ENV === "production"
      ? process.env.MONGO_URI
      : "mongodb://localhost:27017/pg-management";

    console.log("Connecting to MongoDB...");

    const conn = await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
    });

    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error(`MongoDB connection error: ${error.message}`);
    console.log("Continuing without database connection for testing...");
    // Don't exit, continue without database for testing
    // process.exit(1);
  }
};

module.exports = connectDB;
