/* Premium Room Cards Styling */

/* Room Cards Container */
.premium-room-container {
  padding: 1rem;
  position: relative;
  width: 100%;
  max-width: 100%;
}

@media (min-width: 640px) {
  .premium-room-container {
    padding: 1.5rem;
  }
}

/* Room Header */
.premium-room-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

@media (min-width: 640px) {
  .premium-room-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
}

.premium-room-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1f38;
  position: relative;
  padding-left: 0.75rem;
  line-height: 1.3;
}

@media (min-width: 640px) {
  .premium-room-title {
    font-size: 1.5rem;
  }
}

.premium-room-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.25rem;
  bottom: 0.25rem;
  width: 4px;
  background: linear-gradient(to bottom, #6366f1, #4f46e5);
  border-radius: 2px;
}

/* Search and Filter Section */
.premium-room-controls {
  background: white;
  border-radius: 0.75rem;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 640px) {
  .premium-room-controls {
    flex-direction: row;
    align-items: center;
    border-radius: 1rem;
    padding: 1.5rem;
  }
}

.premium-room-search {
  position: relative;
  flex: 1;
}

.premium-room-search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  background-color: #f8fafc;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.premium-room-search-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background-color: white;
}

.premium-room-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  transition: color 0.3s ease;
}

.premium-room-search-input:focus + .premium-room-search-icon {
  color: #6366f1;
}

.premium-room-filter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.premium-room-filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
}

.premium-room-filter-select {
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  background-color: #f8fafc;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  color: #1e293b;
  min-width: 150px;
}

.premium-room-filter-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background-color: white;
}

/* Add Room Button */
.premium-room-add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: white;
  background: linear-gradient(to right, #6366f1, #4f46e5);
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.2);
  text-decoration: none;
}

@media (min-width: 640px) {
  .premium-room-add-btn {
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
  }
}

.premium-room-add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 10px -1px rgba(99, 102, 241, 0.3);
}

.premium-room-add-btn:active {
  transform: translateY(0);
}

.premium-room-add-icon {
  margin-right: 0.5rem;
}

/* Room Cards Grid */
.premium-room-cards {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

@media (min-width: 640px) {
  .premium-room-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .premium-room-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1280px) {
  .premium-room-cards {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Empty State */
.premium-room-empty {
  background: white;
  border-radius: 0.75rem;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@media (min-width: 640px) {
  .premium-room-empty {
    border-radius: 1rem;
    padding: 3rem;
  }
}

.premium-room-empty-text {
  color: #64748b;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .premium-room-empty-text {
    font-size: 1rem;
  }
}

/* Loading Spinner */
.premium-room-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.premium-room-spinner {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: 4px solid #e2e8f0;
  border-top-color: #6366f1;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
