/* Premium Sidebar Styling */

/* Main sidebar container */
.premium-sidebar {
  background: linear-gradient(135deg, #1a1f38 0%, #2d3250 100%);
  background-size: 200% 200%;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  border-right: 1px solid rgba(255, 255, 255, 0.07);
  /* animation: gradientShift 15s ease infinite alternate; */
}

/* @keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
} */

/* Sidebar overlay pattern */
.premium-sidebar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.4;
  z-index: 0;
}

/* Sidebar content */
.premium-sidebar-content {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* Logo section */
.premium-sidebar-logo {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
}

.premium-sidebar-logo:hover {
  background: rgba(255, 255, 255, 0.03);
}

/* User profile section */
.premium-sidebar-profile {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
  transition: all 0.3s ease;
}

.premium-sidebar-profile:hover {
  background: rgba(255, 255, 255, 0.04);
}

/* Navigation menu */
.premium-sidebar-nav {
  padding: 1.25rem 0.75rem;
}

/* Navigation links */
.premium-sidebar-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Link hover effect */
.premium-sidebar-link:hover {
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.07);
  transform: translateX(3px);
}

/* Link active state */
.premium-sidebar-link.active {
  color: #ffffff;
  background: linear-gradient(
    90deg,
    rgba(99, 102, 241, 0.8) 0%,
    rgba(79, 70, 229, 0.8) 100%
  );
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
  animation: activeLink 0.3s ease-in-out;
}

@keyframes activeLink {
  0% {
    transform: translateX(0);
    opacity: 0.7;
  }
  50% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Active link indicator */
.premium-sidebar-link.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: #fff;
  border-radius: 0 4px 4px 0;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
  /* animation: glowPulse 1.5s infinite alternate; */
}

/* @keyframes glowPulse {
  from {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  to {
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
  }
} */

/* Link icon */
.premium-sidebar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  margin-right: 0.75rem;
  border-radius: 0.375rem;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* Icon background effect */
.premium-sidebar-icon::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Active link icon */
.premium-sidebar-link.active .premium-sidebar-icon {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
}

.premium-sidebar-link.active .premium-sidebar-icon::after {
  opacity: 1;
}

/* Link hover icon effect */
.premium-sidebar-link:hover .premium-sidebar-icon {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.15);
}

.premium-sidebar-link:hover .premium-sidebar-icon::after {
  opacity: 0.5;
}

/* Link text */
.premium-sidebar-text {
  font-size: 0.95rem;
  letter-spacing: 0.01em;
}

/* Mobile sidebar backdrop */
.premium-sidebar-backdrop {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
}

/* Sidebar footer */
.premium-sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.4);
  text-align: center;
  margin-top: auto;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.2));
  position: relative;
  overflow: hidden;
}

.premium-sidebar-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.01),
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.01)
  );
}

/* Responsive adjustments */
@media (max-width: 1023px) {
  .premium-sidebar {
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);
  }
}

/* Sidebar Container */
.sidebar-container {
  height: 100%;
  background: linear-gradient(180deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

/* Sidebar Links */
.sidebar-link {
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, transform 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.sidebar-link:hover {
  transform: translateX(3px);
}

.sidebar-link.active {
  transform: translateX(0);
}

.sidebar-link.active::before {
  transition: box-shadow 0.3s ease-in-out;
}

/* User Profile Section */
.sidebar-profile {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s ease;
}

.sidebar-profile-avatar {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.sidebar-profile-avatar:hover {
  transform: scale(1.05);
}

/* Logo Section */
.sidebar-logo {
  transition: background-color 0.3s ease;
}

.sidebar-logo:hover {
  transform: scale(1.02);
}

/* Logout Button */
.sidebar-logout {
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
}

.sidebar-logout:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateX(4px);
}

/* Mobile Backdrop */
.sidebar-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  transition: opacity 0.3s ease-in-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar-container {
    position: fixed;
    z-index: 50;
  }
}
