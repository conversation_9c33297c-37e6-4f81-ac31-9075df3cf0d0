/* Premium Tenant Form Styling */

/* Container */
.premium-tenant-container {
  padding: 1rem;
  position: relative;
  width: 100%;
  max-width: 100%;
}

@media (min-width: 640px) {
  .premium-tenant-container {
    padding: 1.5rem;
  }
}

/* Header */
.premium-tenant-header {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.25rem;
}

@media (min-width: 640px) {
  .premium-tenant-header {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
}

.premium-tenant-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1f38;
  position: relative;
  padding-left: 0.75rem;
  line-height: 1.3;
}

@media (min-width: 640px) {
  .premium-tenant-title {
    font-size: 1.5rem;
  }
}

.premium-tenant-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.25rem;
  bottom: 0.25rem;
  width: 4px;
  background: linear-gradient(to bottom, #6366f1, #4f46e5);
  border-radius: 2px;
}

/* Back button */
.premium-tenant-back {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: white;
  background: linear-gradient(to right, #6366f1, #4f46e5);
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.2);
  text-decoration: none;
}

.premium-tenant-back:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 10px -1px rgba(99, 102, 241, 0.3);
}

.premium-tenant-back:active {
  transform: translateY(0);
}

.premium-tenant-back-icon {
  margin-right: 0.5rem;
  transition: transform 0.3s ease;
}

.premium-tenant-back:hover .premium-tenant-back-icon {
  transform: translateX(-3px);
}

/* Form Card */
.premium-tenant-form-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  animation: formEntrance 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes formEntrance {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.premium-tenant-form-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
  z-index: 0;
}

.premium-tenant-form-body {
  padding: 1.5rem;
  position: relative;
  z-index: 1;
}

/* Form Grid */
.premium-tenant-form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.25rem;
}

@media (min-width: 768px) {
  .premium-tenant-form-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

/* Form Group */
.premium-tenant-form-group {
  position: relative;
  transition: all 0.3s ease;
  transform: translateY(0);
}

.premium-tenant-form-group:hover {
  transform: translateY(-2px);
}

/* Form Label */
.premium-tenant-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.premium-tenant-form-group:focus-within .premium-tenant-form-label {
  color: #4f46e5;
  transform: translateX(5px);
}

/* Form Input */
.premium-tenant-form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  color: #1f2937;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.premium-tenant-form-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background-color: white;
}

.premium-tenant-form-input:hover {
  border-color: #d1d5db;
  background-color: white;
}

/* Form Select */
.premium-tenant-form-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
  color: #1f2937;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.premium-tenant-form-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background-color: white;
}

.premium-tenant-form-select:hover {
  border-color: #d1d5db;
  background-color: white;
}

/* Form Divider */
.premium-tenant-form-divider {
  border-top: 1px solid #e5e7eb;
  margin: 2rem 0;
  position: relative;
}

.premium-tenant-form-divider-title {
  position: absolute;
  top: -0.75rem;
  left: 1rem;
  background: white;
  padding: 0 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
}

/* Form Submit Button */
.premium-tenant-submit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  color: white;
  background: linear-gradient(135deg, #6366f1, #4f46e5);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.2);
  border: none;
  cursor: pointer;
  width: 100%;
  margin-top: 1.5rem;
  position: relative;
  overflow: hidden;
}

@media (min-width: 768px) {
  .premium-tenant-submit-btn {
    width: auto;
  }
}

.premium-tenant-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 100%);
  transform: translateX(-100%);
  animation: shimmer 3s infinite;
}

.premium-tenant-submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.3);
}

.premium-tenant-submit-btn:active {
  transform: translateY(-1px);
}

.premium-tenant-submit-icon {
  margin-right: 0.5rem;
  transition: transform 0.3s ease;
}

.premium-tenant-submit-btn:hover .premium-tenant-submit-icon {
  transform: rotate(15deg);
}

/* Preselected Room Alert */
.premium-tenant-alert {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  border-left: 4px solid #10b981;
  color: #065f46;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  animation: alertEntrance 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes alertEntrance {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.premium-tenant-alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.premium-tenant-alert-text {
  font-size: 0.875rem;
  opacity: 0.9;
}

/* Loading Spinner */
.premium-tenant-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.premium-tenant-spinner {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  border: 4px solid #e2e8f0;
  border-top-color: #6366f1;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

.premium-tenant-video-feed {
  width: 100%;
  max-width: 400px;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  margin: 0 auto;
  display: block;
  background: #000;
}

.premium-tenant-ocr-preview img {
  width: 100%;
  max-width: 400px;
  height: auto;
  border-radius: 8px;
  margin: 0 auto;
  display: block;
  border: 2px solid #e5e7eb;
}

.premium-tenant-aadhaar-in-form-preview img {
  width: 100%;
  max-width: 300px;
  height: auto;
  border-radius: 8px;
  margin: 0 auto;
  display: block;
  border: 2px solid #e5e7eb;
}

.premium-tenant-live-capture {
  background: #f9fafb;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.premium-tenant-capture-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.premium-tenant-btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
}

.premium-tenant-capture-btn {
  background: #10b981;
  color: white;
}

.premium-tenant-capture-btn:hover {
  background: #059669;
}

.premium-tenant-retake-btn {
  background: #3b82f6;
  color: white;
}

.premium-tenant-retake-btn:hover {
  background: #2563eb;
}

.premium-tenant-cancel-btn {
  background: #ef4444;
  color: white;
}

.premium-tenant-cancel-btn:hover {
  background: #dc2626;
}

.premium-tenant-ocr-upload {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .premium-tenant-ocr-upload {
    flex-direction: row;
  }
}

.premium-tenant-ocr-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  background: #f3f4f6;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  flex: 1;
}

.premium-tenant-ocr-label:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.premium-tenant-live-capture-btn {
  background: #3b82f6;
  color: white;
  border: none;
}

.premium-tenant-live-capture-btn:hover {
  background: #2563eb;
}

.premium-tenant-ocr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: #f9fafb;
  border-radius: 8px;
  margin-top: 1rem;
}

.premium-tenant-ocr-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.premium-tenant-ocr-note {
  text-align: center;
  color: #6b7280;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

.premium-tenant-review-grid-container {
  width: 100%;
  margin-top: 0.5rem;
}

.premium-tenant-review-grid {
  display: grid;
  grid-template-columns: 160px 1fr;
  gap: 0.75rem 2rem;
  align-items: center;
  background: #f8fafc;
  border-radius: 10px;
  padding: 1.2rem 2rem;
  margin-bottom: 0.5rem;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 4px rgba(0,0,0,0.03);
}

.premium-tenant-review-grid .label {
  text-align: right;
  font-weight: 700;
  color: #334155;
  font-size: 1rem;
  letter-spacing: 0.01em;
  padding-right: 0.5rem;
}

.premium-tenant-review-grid .value {
  text-align: left;
  color: #111827;
  font-size: 1rem;
  word-break: break-word;
  background: #fff;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  min-height: 2rem;
  display: flex;
  align-items: center;
}

.premium-tenant-review-item.flex.items-center {
  align-items: center;
  gap: 2rem;
}

.premium-tenant-review-photo {
  margin-bottom: 0;
  display: block;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}
