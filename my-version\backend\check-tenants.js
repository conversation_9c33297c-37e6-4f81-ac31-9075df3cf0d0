const mongoose = require('mongoose');
const Tenant = require('./models/Tenant');

async function checkTenants() {
  try {
    await mongoose.connect('mongodb://localhost:27017/pg-management');
    
    const tenants = await Tenant.find();
    console.log('Total tenants:', tenants.length);
    
    if (tenants.length > 0) {
      tenants.forEach(tenant => {
        console.log(`- ${tenant.name} (Room: ${tenant.roomId}, Bed: ${tenant.bedNumber})`);
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkTenants();
