import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { FaCamera, FaArrowRight, FaArrowLeft, FaCheckCircle, FaMobile, FaIdCard, FaUser } from 'react-icons/fa';
import { showToast } from '../utils/toast';
import TenantForm from './TenantForm';
import { useRooms } from '../context/RoomContext';

const TenantOnboarding = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { rooms } = useRooms();
  const [currentStep, setCurrentStep] = useState(1);
  const [mobileNumber, setMobileNumber] = useState('');
  const [tenantPhoto, setTenantPhoto] = useState(null);
  const [aadhaarData, setAadhaarData] = useState(null);
  const [fullFormData, setFullFormData] = useState(null);

  // Get parameters from URL query parameters if available
  const queryParams = new URLSearchParams(location.search);
  const preselectedRoomId = queryParams.get("roomId");
  const preselectedBedNumber = queryParams.get("bedNumber");
  const returnToRoom = queryParams.get("returnToRoom") === "true";

  const steps = [
    { id: 1, title: 'Mobile Number Verification', icon: FaMobile },
    { id: 2, title: 'Live Photo Capture', icon: FaCamera },
    { id: 3, title: 'Aadhaar Card Upload', icon: FaIdCard },
    { id: 4, title: 'Additional Details', icon: FaUser },
    { id: 5, title: 'Review & Submit', icon: FaCheckCircle }
  ];

  const handleMobileSubmit = (e) => {
    e.preventDefault();
    if (mobileNumber.length === 10) {
      setCurrentStep(2);
    } else {
      showToast('Please enter a valid 10-digit mobile number', { type: 'error' });
    }
  };

  const handleTenantPhotoCaptured = (photoUrl) => {
    setTenantPhoto(photoUrl);
    setCurrentStep(3);
  };

  const handleAadhaarData = (data) => {
    setAadhaarData(data);
    setFullFormData(prev => ({ ...prev, ...data }));
  };

  const handleAadhaarReviewComplete = (aadhaarFormData) => {
    setAadhaarData(aadhaarFormData);
    setFullFormData(prev => ({ ...prev, ...aadhaarFormData }));
    setCurrentStep(4);
  };

  const handleAdditionalDetailsComplete = (data) => {
    setFullFormData(prev => ({ 
      ...prev, 
      ...data,
      mobileNumber: mobileNumber,
      tenantPhoto: tenantPhoto,
    }));
    setCurrentStep(5);
  };

  const handleSubmitFinal = () => {
    console.log("Final Tenant Data:", fullFormData);
    showToast("Tenant onboarding complete!", { type: "success" });
    if (returnToRoom && preselectedRoomId) {
      navigate(`/rooms/details/${preselectedRoomId}`);
    } else {
      navigate('/tenants');
    }
  };

  const getRoomNumber = (roomId) => {
    const room = rooms.find(r => r._id === roomId);
    return room ? room.roomNumber : 'N/A';
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="premium-tenant-step">
            <h2 className="text-2xl font-bold mb-6">Enter Mobile Number</h2>
            <form onSubmit={handleMobileSubmit} className="space-y-4">
              <div className="premium-tenant-form-group">
                <input
                  type="tel"
                  value={mobileNumber}
                  onChange={(e) => setMobileNumber(e.target.value)}
                  placeholder="Enter 10-digit mobile number"
                  className="premium-tenant-form-input"
                  maxLength="10"
                  required
                />
              </div>
              <button type="submit" className="premium-tenant-submit-btn">
                Continue <FaArrowRight className="ml-2" />
              </button>
            </form>
          </div>
        );

      case 2:
        return (
          <div className="premium-tenant-step">
            <h2 className="text-2xl font-bold mb-6">Capture Live Photo</h2>
            <TenantForm 
              formMode="tenantPhotoCapture" 
              onComplete={handleTenantPhotoCaptured} 
            />
          </div>
        );

      case 3:
        return (
          <div className="premium-tenant-step">
            <h2 className="text-2xl font-bold mb-6">Upload Aadhaar Card</h2>
            <TenantForm
              formMode="aadhaarUpload"
              onAadhaarData={handleAadhaarData}
              onComplete={handleAadhaarReviewComplete}
            />
          </div>
        );

      case 4:
        return (
          <div className="premium-tenant-step">
            <h2 className="text-2xl font-bold mb-6">Additional Details</h2>
            <TenantForm
              formMode="additionalDetails"
              initialData={fullFormData}
              onComplete={handleAdditionalDetailsComplete}
            />
          </div>
        );

      case 5:
        return (
          <div className="premium-tenant-step">
            <h2 className="text-2xl font-bold mb-6">Review & Submit</h2>
            <div className="space-y-6">
              <div className="premium-tenant-review-item">
                <FaCheckCircle className="text-green-500 mr-2" />
                <span>Mobile Number: {mobileNumber}</span>
              </div>
              {tenantPhoto && (
                <div className="premium-tenant-review-item flex items-center gap-6">
                  <img
                    src={tenantPhoto}
                    alt="Tenant Captured"
                    className="w-32 h-32 object-cover rounded premium-tenant-review-photo"
                  />
                  <div className="flex flex-col justify-center">
                    <div className="flex items-center mb-2">
                      <FaCheckCircle className="text-green-500 mr-2" />
                      <span className="font-semibold">Tenant Photo Captured</span>
                    </div>
                  </div>
                </div>
              )}
              {aadhaarData && (
                <div className="premium-tenant-review-item flex-col items-start premium-tenant-review-grid-container">
                  <span className="font-semibold text-lg mb-2">Aadhaar Details Verified</span>
                  <div className="premium-tenant-review-grid">
                    <div className="label">Name:</div><div className="value">{aadhaarData.name || 'N/A'}</div>
                    <div className="label">Aadhaar No:</div><div className="value">{aadhaarData.idProofNumber || 'N/A'}</div>
                    <div className="label">DOB:</div><div className="value">{aadhaarData.dob || 'N/A'}</div>
                    <div className="label">Gender:</div><div className="value">{aadhaarData.gender || 'N/A'}</div>
                    <div className="label">Address:</div><div className="value">{aadhaarData.address || 'N/A'}</div>
                  </div>
                </div>
              )}
              {fullFormData && (
                <div className="premium-tenant-review-item flex-col items-start premium-tenant-review-grid-container">
                  <span className="font-semibold text-lg mb-2">Additional Details</span>
                  <div className="premium-tenant-review-grid">
                    <div className="label">Email:</div><div className="value">{fullFormData.email || 'N/A'}</div>
                    <div className="label">Occupation:</div><div className="value">{fullFormData.occupation || 'N/A'}</div>
                    <div className="label">Room:</div><div className="value">{getRoomNumber(fullFormData.roomId)}</div>
                    <div className="label">Bed:</div><div className="value">{fullFormData.bedNumber || 'N/A'}</div>
                    <div className="label">Joining Date:</div><div className="value">{fullFormData.joiningDate || 'N/A'}</div>
                  </div>
                </div>
              )}
              <button
                onClick={handleSubmitFinal}
                className="premium-tenant-submit-btn"
              >
                Complete Onboarding
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="premium-tenant-onboarding">
      <div className="premium-tenant-steps">
        {steps.map((step) => (
          <div
            key={step.id}
            className={`premium-tenant-step-indicator ${
              currentStep >= step.id ? 'active' : ''
            }`}
          >
            <div className="premium-tenant-step-number">
              <step.icon className="text-xl" />
            </div>
            <div className="premium-tenant-step-title">{step.title}</div>
          </div>
        ))}
      </div>

      <div className="premium-tenant-content">
        {renderStep()}
      </div>

      {currentStep > 1 && currentStep < 5 && (
        <button
          onClick={() => {
            setCurrentStep(currentStep - 1);
          }}
          className="premium-tenant-back-btn"
        >
          <FaArrowLeft className="mr-2" /> Back
        </button>
      )}
    </div>
  );
};

export default TenantOnboarding; 