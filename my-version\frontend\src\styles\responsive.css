/* Comprehensive Responsive Utilities */

/* Base Container */
.responsive-container {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
  margin-right: auto;
  margin-left: auto;
  max-width: 100%;
  overflow-x: hidden;
}

@media (min-width: 640px) {
  .responsive-container {
    padding-right: 1.5rem;
    padding-left: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    padding-right: 2rem;
    padding-left: 2rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    padding-right: 2.5rem;
    padding-left: 2.5rem;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    padding-right: 3rem;
    padding-left: 3rem;
  }
}

/* Responsive Grid System */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem;
  width: 100%;
}

@media (min-width: 640px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 768px) {
  .responsive-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.75rem;
  }
}

@media (min-width: 1280px) {
  .responsive-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Responsive Typography */
.responsive-title {
  font-size: 1.25rem;
  line-height: 1.3;
  font-weight: 600;
}

@media (min-width: 640px) {
  .responsive-title {
    font-size: 1.5rem;
    line-height: 1.35;
  }
}

@media (min-width: 768px) {
  .responsive-title {
    font-size: 1.75rem;
    line-height: 1.4;
  }
}

@media (min-width: 1024px) {
  .responsive-title {
    font-size: 2rem;
    line-height: 1.4;
  }
}

.responsive-subtitle {
  font-size: 1rem;
  line-height: 1.4;
  font-weight: 500;
}

@media (min-width: 640px) {
  .responsive-subtitle {
    font-size: 1.125rem;
    line-height: 1.45;
  }
}

@media (min-width: 768px) {
  .responsive-subtitle {
    font-size: 1.25rem;
    line-height: 1.5;
  }
}

.responsive-text {
  font-size: 0.875rem;
  line-height: 1.5;
}

@media (min-width: 640px) {
  .responsive-text {
    font-size: 1rem;
    line-height: 1.6;
  }
}

/* Responsive Flex Layouts */
.responsive-flex {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 640px) {
  .responsive-flex {
    flex-direction: row;
    align-items: center;
    gap: 1.25rem;
  }
}

.responsive-flex-between {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 640px) {
  .responsive-flex-between {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

/* Responsive Cards */
.responsive-card {
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

@media (min-width: 640px) {
  .responsive-card {
    padding: 1.25rem;
    border-radius: 1rem;
  }
}

@media (min-width: 768px) {
  .responsive-card {
    padding: 1.5rem;
  }
}

/* Responsive Form Elements */
.responsive-form-group {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .responsive-form-group {
    margin-bottom: 1.25rem;
  }
}

.responsive-input {
  width: 100%;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  height: 2.5rem;
  line-height: 1.25;
}

@media (min-width: 640px) {
  .responsive-input {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    height: 2.75rem;
  }
}

.responsive-button {
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  width: 100%;
}

@media (min-width: 640px) {
  .responsive-button {
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    width: auto;
  }
}

/* Responsive Tables */
.responsive-table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.responsive-table {
  min-width: 100%;
  border-collapse: collapse;
}

.responsive-table th,
.responsive-table td {
  padding: 0.5rem;
  font-size: 0.75rem;
}

@media (min-width: 640px) {
  .responsive-table th,
  .responsive-table td {
    padding: 0.75rem;
    font-size: 0.875rem;
  }
}

@media (min-width: 768px) {
  .responsive-table th,
  .responsive-table td {
    padding: 1rem;
    font-size: 1rem;
  }
}

/* Responsive Spacing */
.responsive-mt {
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .responsive-mt {
    margin-top: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-mt {
    margin-top: 2rem;
  }
}

.responsive-mb {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .responsive-mb {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-mb {
    margin-bottom: 2rem;
  }
}

.responsive-p {
  padding: 1rem;
  width: 100%;
}

@media (min-width: 640px) {
  .responsive-p {
    padding: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-p {
    padding: 2rem;
  }
}

/* Responsive Visibility */
.hide-on-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-on-mobile {
    display: block;
  }
}

.show-on-mobile {
  display: block;
}

@media (min-width: 640px) {
  .show-on-mobile {
    display: none;
  }
}

/* Responsive Images */
.responsive-img {
  max-width: 100%;
  height: auto;
}

/* Responsive Icons */
.responsive-icon {
  width: 1.25rem;
  height: 1.25rem;
}

@media (min-width: 640px) {
  .responsive-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-icon {
    width: 1.75rem;
    height: 1.75rem;
  }
}

/* Search input with icon */
.responsive-input-with-icon {
  position: relative;
}

.responsive-input-with-icon input {
  padding-left: 2.5rem;
}

.responsive-input-with-icon .search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
  z-index: 10;
}

/* Reset for sidebar elements */
.responsive-sidebar,
.responsive-sidebar * {
  box-sizing: border-box;
}

/* Responsive Sidebar */
.responsive-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 50;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  will-change: transform; /* Optimize for animations */
  -webkit-backface-visibility: hidden; /* Prevent blurring on some devices */
  backface-visibility: hidden;
  flex-shrink: 0; /* Prevent sidebar from shrinking */
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.responsive-sidebar.open {
  transform: translateX(0);
  -webkit-transform: translateX(0);
}

@media (min-width: 768px) {
  .responsive-sidebar {
    width: 16rem;
    position: static; /* Changed from relative to static */
    transform: none !important;
    -webkit-transform: none !important;
    display: block !important;
    flex-shrink: 0; /* Prevent sidebar from shrinking */
    margin: 0;
    padding: 0;
    left: 0;
  }
}

@media (min-width: 1024px) {
  .responsive-sidebar {
    width: 18rem;
    position: static; /* Changed from relative to static */
    transform: none !important;
    -webkit-transform: none !important;
    display: block !important;
    flex-shrink: 0; /* Prevent sidebar from shrinking */
    margin: 0;
    padding: 0;
    left: 0;
  }
}
