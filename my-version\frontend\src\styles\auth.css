/* Auth Forms Animations and Transitions */

/* Input field focus animation */
.auth-input-focus {
  transition: all 0.3s ease-in-out;
}

.auth-input-focus:focus {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Card entrance animation */
/* @keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.card-entrance {
  animation: cardEntrance 0.8s ease-out;
} */

/* Button hover effect */
.btn-hover-effect {
  transition: all 0.3s ease;
}

.btn-hover-effect:hover {
  transform: translateY(-3px);
  box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.btn-hover-effect:active {
  transform: translateY(-1px);
}

/* Password toggle button animation */
.password-toggle {
  transition: all 0.2s ease;
}

.password-toggle:hover {
  transform: scale(1.1);
}

/* Form shake animation for errors */
/* @keyframes shake {
  0%, 100% {transform: translateX(0);}
  10%, 30%, 50%, 70%, 90% {transform: translateX(-5px);}
  20%, 40%, 60%, 80% {transform: translateX(5px);}
}

.shake-animation {
  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
} */

/* Input field floating label effect */
.input-container {
  position: relative;
  margin-bottom: 1rem;
}

.floating-label {
  position: absolute;
  pointer-events: none;
  left: 15px;
  top: 15px;
  transition: 0.2s ease all;
  color: rgba(255, 255, 255, 0.6);
}

.auth-input:focus ~ .floating-label,
.auth-input:not(:placeholder-shown) ~ .floating-label {
  top: -10px;
  left: 10px;
  font-size: 12px;
  opacity: 1;
  color: white;
  background: rgba(59, 130, 246, 0.5);
  padding: 0 5px;
  border-radius: 4px;
}

/* Glassmorphism hover effect */
.glass-effect {
  transition: all 0.5s ease;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Fade in animation */
/* @keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
} */

/* Smooth transition for form elements */
.form-element {
  transition: all 0.3s ease-in-out;
}

.form-element:hover {
  transform: translateY(-2px);
}

/* Loading spinner animation */
/* @keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
} */

/* Error message animation */
/* @keyframes slideIn {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.error-message {
  animation: slideIn 0.3s ease-out;
} */

/* Form field focus effect */
.form-field-focus {
  transition: all 0.3s ease;
}

.form-field-focus:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* Button loading state */
.button-loading {
  position: relative;
  pointer-events: none;
}

/* .button-loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin: -10px 0 0 -10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
} */

/* Form container animation */
/* .form-container {
  animation: cardEntrance 0.6s ease-out;
} */

/* Input group animation */
.input-group {
  transition: all 0.3s ease;
}

.input-group:hover {
  transform: translateY(-2px);
}

/* Link hover effect */
.auth-link {
  transition: all 0.2s ease;
}

.auth-link:hover {
  transform: translateY(-1px);
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Background gradient animation */
/* @keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animated-gradient {
  background-size: 200% 200%;
  animation: gradientShift 15s ease infinite;
} */

/* Form validation animation */
/* @keyframes validPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.valid-input {
  animation: validPulse 0.5s ease-out;
} */

/* Form submission animation */
/* @keyframes submitPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

.submitting {
  animation: submitPulse 0.3s ease-in-out;
} */
