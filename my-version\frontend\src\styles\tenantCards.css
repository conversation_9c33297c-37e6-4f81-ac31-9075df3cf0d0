/* Tenant Cards Styling */

.tenant-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 1.25rem;
  padding: 1rem;
}

.tenant-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  cursor: pointer;
}

.tenant-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Card header */
.tenant-card-header {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
}

.tenant-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tenant-card-title-icon {
  color: white;
}

.tenant-card-subtitle {
  display: flex;
  justify-content: space-between;
  color: #4a5568;
  font-size: 0.875rem;
  gap: 0.5rem;
}

.tenant-info-badge {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.tenant-info-icon {
  margin-right: 0.5rem;
}

/* Card body */
.tenant-card-body {
  padding: 1.25rem;
  flex-grow: 1;
}

.tenant-info-section {
  margin-bottom: 1.25rem;
}

.tenant-info-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.tenant-info-item:last-child {
  border-bottom: none;
}

.tenant-info-icon {
  color: #6366f1;
  margin-right: 0.75rem;
}

.tenant-info-label {
  color: #4a5568;
  font-size: 0.875rem;
  width: 80px;
}

.tenant-info-value {
  color: #2d3748;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Amenities section */
.tenant-amenities {
  margin-bottom: 1.25rem;
}

.tenant-amenities-title {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.tenant-amenities-icon {
  margin-right: 0.5rem;
  color: #6366f1;
}

.tenant-amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tenant-amenity {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  color: #4a5568;
  display: flex;
  align-items: center;
}

.tenant-amenity-icon {
  margin-right: 0.25rem;
  color: #6366f1;
}

/* Status section */
.tenant-card-status {
  margin-top: auto;
  padding: 0.75rem 1.25rem;
  text-align: center;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem 1rem;
  border-radius: 9999px;
  font-weight: 600;
  font-size: 0.75rem;
  width: 100%;
}

.status-active {
  background: linear-gradient(to right, #10b981, #059669);
  color: white;
}

.status-inactive {
  background: linear-gradient(to right, #ef4444, #dc2626);
  color: white;
}

/* Card actions */
.tenant-card-actions {
  display: flex;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: #f9fafb;
  gap: 0.5rem;
}

.tenant-card-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex: 1;
  border: none;
  cursor: pointer;
}

.tenant-card-btn-edit {
  background: linear-gradient(to right, #6366f1, #4f46e5);
  color: white;
}

.tenant-card-btn-edit:hover {
  background: linear-gradient(to right, #4f46e5, #4338ca);
}

.tenant-card-btn-delete {
  background: linear-gradient(to right, #ef4444, #dc2626);
  color: white;
}

.tenant-card-btn-delete:hover {
  background: linear-gradient(to right, #dc2626, #b91c1c);
}

.tenant-card-btn-icon {
  margin-right: 0.375rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .tenant-cards-container {
    grid-template-columns: 1fr;
    padding: 0.75rem;
  }
  
  .room-card {
    max-width: 100%;
  }
}

/* Tenant-specific color overrides */
.room-card-header {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  box-shadow: 0 2px 10px rgba(99, 102, 241, 0.2);
  padding: 1rem;
}

.room-card-info-icon {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  box-shadow: 0 2px 5px rgba(99, 102, 241, 0.3);
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  margin-right: 0.75rem;
}

.room-card-info-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  transition: all 0.2s ease;
}

.room-card-info-item:hover .room-card-info-icon {
  box-shadow: 0 3px 8px rgba(99, 102, 241, 0.4);
  transform: translateY(-1px);
}

.room-card-info-item:hover .room-card-info-value {
  color: #4f46e5;
}

.room-card-info-label {
  font-size: 0.875rem;
  color: #6b7280;
  width: 80px;
}

.room-card-info-value {
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
  flex: 1;
  transition: color 0.2s ease;
}

.room-card-amenities-icon,
.room-card-amenity-icon {
  color: #6366f1;
}

.room-card-btn-edit {
  background: linear-gradient(to right, #6366f1, #4f46e5);
}

.room-card-btn-edit:hover {
  background: linear-gradient(to right, #4f46e5, #4338ca);
}

/* Animations */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(25px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.room-card {
  animation: cardEntrance 0.5s ease-out forwards;
  max-width: 320px;
  margin: 0 auto;
  width: 100%;
}

/* Staggered animation for multiple cards */
.tenant-cards-container .room-card:nth-child(1) { animation-delay: 0.1s; }
.tenant-cards-container .room-card:nth-child(2) { animation-delay: 0.2s; }
.tenant-cards-container .room-card:nth-child(3) { animation-delay: 0.3s; }
.tenant-cards-container .room-card:nth-child(4) { animation-delay: 0.4s; }
.tenant-cards-container .room-card:nth-child(5) { animation-delay: 0.5s; }
.tenant-cards-container .room-card:nth-child(6) { animation-delay: 0.6s; }
