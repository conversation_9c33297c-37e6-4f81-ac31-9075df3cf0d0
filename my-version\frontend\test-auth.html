<!DOCTYPE html>
<html>
<head>
    <title>Test Auth</title>
</head>
<body>
    <h1>Authentication Test</h1>
    <div id="result"></div>
    
    <script>
        const token = localStorage.getItem('token');
        const resultDiv = document.getElementById('result');
        
        if (token) {
            resultDiv.innerHTML = `
                <p>Token exists: YES</p>
                <p>Token: ${token.substring(0, 50)}...</p>
            `;
            
            // Test API call
            fetch('http://localhost:5000/api/v1/tenants', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                resultDiv.innerHTML += `<p>API Response: ${JSON.stringify(data)}</p>`;
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML += `<p>API Error: ${error.message}</p>`;
            });
        } else {
            resultDiv.innerHTML = '<p>Token exists: NO</p>';
        }
    </script>
</body>
</html>
