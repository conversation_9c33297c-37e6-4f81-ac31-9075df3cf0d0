const mongoose = require('mongoose');
const Room = require('./models/Room');
const Tenant = require('./models/Tenant');
const Admin = require('./models/Admin');

async function testDatabase() {
  try {
    await mongoose.connect('mongodb://localhost:27017/pg-management');
    console.log('Connected to MongoDB');

    const admins = await Admin.find();
    console.log('Total Admins:', admins.length);

    // Check all tenants regardless of admin
    const allTenants = await Tenant.find();
    console.log('Total Tenants in DB:', allTenants.length);

    if (allTenants.length > 0) {
      console.log('All tenants:');
      allTenants.forEach(tenant => {
        console.log(`- ${tenant.name} (Admin: ${tenant.adminId})`);
      });
    }

    if (admins.length > 0) {
      console.log('First Admin:', admins[0].name, admins[0]._id);

      const rooms = await Room.find({ adminId: admins[0]._id });
      console.log('Rooms for admin:', rooms.length);

      if (rooms.length > 0) {
        console.log('First room:', rooms[0].roomNumber, 'ID:', rooms[0]._id);

        // Create a test tenant if none exist
        const tenants = await Tenant.find({ adminId: admins[0]._id });
        console.log('Tenants for admin:', tenants.length);

        if (tenants.length === 0) {
          console.log('Creating test tenant...');
          const testTenant = new Tenant({
            name: 'Test Tenant',
            phone: '1234567890',
            email: '<EMAIL>',
            idProofType: 'Aadhar',
            idProofNumber: 'TEST123456',
            roomId: rooms[0]._id,
            bedNumber: 1,
            adminId: admins[0]._id,
            occupation: 'Student'
          });

          await testTenant.save();
          console.log('Test tenant created successfully!');
        } else {
          console.log('First tenant:', tenants[0].name);
        }
      }
    }

    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

testDatabase();
